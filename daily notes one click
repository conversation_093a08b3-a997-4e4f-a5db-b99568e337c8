import os
import re
import json
import sys
import platform
import subprocess
from datetime import date
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

APP_TITLE = "Open Today's Note"
DATE_FMT = "%d.%m.%Y"  # -> 04.08.2025

# ---- Hardcoded root (used on first run unless a saved default exists)
DEFAULT_ROOT = Path(r"C:\Users\<USER>\OneDrive - nsslgroup.co.uk\Desktop\DAILY NOTES 2025")

# ---- Config file location (per-user, no extra dependencies)
CONFIG_APP_DIRNAME = "OpenTodaysNote"
CONFIG_FILENAME = "config.json"

def _config_path() -> Path:
    if platform.system() == "Windows":
        base = Path(os.environ.get("APPDATA", str(Path.home())))
    elif platform.system() == "Darwin":
        base = Path.home() / "Library" / "Application Support"
    else:
        base = Path.home() / ".config"
    return base / CONFIG_APP_DIRNAME / CONFIG_FILENAME

def load_saved_root() -> Path | None:
    cfg = _config_path()
    try:
        if cfg.exists():
            data = json.loads(cfg.read_text(encoding="utf-8"))
            p = Path(data.get("root", ""))
            if p.exists():
                return p
    except Exception:
        pass
    return None

def save_default_root(root: Path) -> None:
    cfg = _config_path()
    cfg.parent.mkdir(parents=True, exist_ok=True)
    cfg.write_text(json.dumps({"root": str(root)}, indent=2), encoding="utf-8")

MONTH_NAMES = [
    "January","February","March","April","May","June",
    "July","August","September","October","November","December"
]

VOID_PAT = re.compile(r"\bVOID\b", re.IGNORECASE)
PREVIOUS_CONTAINER_PAT = re.compile(r"previous\s+clients", re.IGNORECASE)
LEADING_NUMBER_PAT = re.compile(r"^\s*\d+\s*\.?\s*")  # strips "4 ", "4.", etc.

def display_name_from_folder(folder_name: str) -> str:
    return LEADING_NUMBER_PAT.sub("", folder_name).strip()

def is_dir(entry: os.DirEntry) -> bool:
    try:
        return entry.is_dir(follow_symlinks=False)
    except Exception:
        return False

def open_with_default_app(path: Path) -> None:
    if platform.system() == "Windows":
        os.startfile(str(path))  # type: ignore[attr-defined]
    elif platform.system() == "Darwin":
        subprocess.Popen(["open", str(path)])
    else:
        subprocess.Popen(["xdg-open", str(path)])

def find_month_dir(client_dir: Path, target_date: date) -> Path | None:
    month_num = f"{target_date.month:02d}"
    month_name = MONTH_NAMES[target_date.month - 1]
    candidates = []

    if not client_dir.exists():
        return None

    for entry in os.scandir(client_dir):
        if not is_dir(entry):
            continue
        name = entry.name
        low = name.lower()
        starts_num = low.startswith(month_num)
        has_name = month_name.lower() in low
        if starts_num or has_name:
            candidates.append(Path(entry.path))

    starts_with_num = [p for p in candidates if p.name.strip().lower().startswith(month_num)]
    if starts_with_num:
        return sorted(starts_with_num, key=lambda p: len(p.name))[0]
    return candidates[0] if candidates else None

def find_today_doc(month_dir: Path, target_date: date) -> Path | None:
    base = target_date.strftime(DATE_FMT)
    preferred = list(month_dir.rglob(base + ".docx"))
    if preferred:
        return preferred[0]
    fallback = list(month_dir.rglob(base + ".doc"))
    if fallback:
        return fallback[0]
    return None

def collect_clients(root: Path) -> list[tuple[str, Path]]:
    clients: list[tuple[str, Path]] = []
    if not root.exists():
        return clients

    def maybe_add(entry: os.DirEntry):
        if not is_dir(entry):
            return
        name = entry.name
        if VOID_PAT.search(name):
            return
        disp = display_name_from_folder(name) or name.strip()
        clients.append((disp, Path(entry.path)))

    for entry in os.scandir(root):
        if not is_dir(entry):
            continue
        name = entry.name
        if PREVIOUS_CONTAINER_PAT.search(name):
            for sub in os.scandir(entry.path):
                maybe_add(sub)
        else:
            maybe_add(entry)

    dedup: dict[str, Path] = {}
    for disp, p in clients:
        if disp not in dedup or len(p.name) < len(dedup[disp].name):
            dedup[disp] = p
    return sorted(dedup.items(), key=lambda x: x[0].lower())

class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title(APP_TITLE)
        self.geometry("760x620")

        self.root_path_var = tk.StringVar(value="")
        self.today = date.today()
        self.today_str = self.today.strftime(DATE_FMT)

        self._build_header()
        self._build_scroll_area()

        # Initialize root: saved default if available, else hardcoded default
        initial = load_saved_root() or DEFAULT_ROOT
        self.use_root(initial, build=True)

    def _build_header(self):
        bar = ttk.Frame(self, padding=8)
        bar.pack(fill=tk.X)

        ttk.Label(bar, text="Root folder:").pack(side=tk.LEFT)
        self.root_entry = ttk.Entry(bar, textvariable=self.root_path_var, width=70)
        self.root_entry.pack(side=tk.LEFT, padx=6)

        ttk.Button(bar, text="Change Root…", command=self.change_root).pack(side=tk.LEFT, padx=(4, 0))
        ttk.Button(bar, text="Set as Default", command=self.set_as_default).pack(side=tk.LEFT, padx=(4, 0))

        ttk.Button(bar, text=f"Today: {self.today_str}", state=tk.DISABLED).pack(side=tk.RIGHT)

    def _build_scroll_area(self):
        outer = ttk.Frame(self, padding=(8, 0, 8, 8))
        outer.pack(fill=tk.BOTH, expand=True)

        self.canvas = tk.Canvas(outer, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(outer, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.inner = ttk.Frame(self.canvas)
        self.inner_id = self.canvas.create_window((0, 0), window=self.inner, anchor="nw")

        self.inner.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))
        self.canvas.bind("<Configure>", self._on_canvas_configure)

    def _on_canvas_configure(self, event):
        self.canvas.itemconfig(self.inner_id, width=event.width)

    def use_root(self, root: Path, build: bool = False):
        self.root_path_var.set(str(root))
        if build:
            self.build_client_buttons(root)

    def change_root(self):
        chosen = filedialog.askdirectory(title="Select the ROOT folder that contains client folders")
        if not chosen:
            return
        self.use_root(Path(chosen), build=True)

    def set_as_default(self):
        p = Path(self.root_path_var.get())
        if not p.exists():
            messagebox.showerror("Path not found", f"Folder does not exist:\n{p}")
            return
        try:
            save_default_root(p)
            messagebox.showinfo("Saved", f"Default root saved:\n{p}\n\nThis will load on next launch.")
        except Exception as e:
            messagebox.showerror("Save failed", f"Could not save default root:\n{e}")

    def build_client_buttons(self, root_dir: Path):
        for child in self.inner.winfo_children():
            child.destroy()

        clients = collect_clients(root_dir)
        if not clients:
            ttk.Label(self.inner, text="No client folders found.", padding=12).grid(row=0, column=0, sticky="w")
            return

        ttk.Label(self.inner, text="Clients", font=("", 12, "bold"), padding=(0, 6)).grid(row=0, column=0, sticky="w", columnspan=4)

        max_cols = 3
        r = 1
        c = 0
        for disp, path in clients:
            b = ttk.Button(self.inner, text=disp, command=lambda p=path: self.open_today_for_client(p))
            b.grid(row=r, column=c, padx=6, pady=6, sticky="ew")
            self.inner.grid_columnconfigure(c, weight=1)

            c += 1
            if c >= max_cols:
                c = 0
                r += 1

    def open_today_for_client(self, client_path: Path):
        month_dir = find_month_dir(client_path, self.today)
        if not month_dir:
            messagebox.showwarning("Month folder not found",
                                   f"No month folder found for {self.today.strftime('%B %Y')}\n\n{client_path}")
            return

        doc = find_today_doc(month_dir, self.today)
        if not doc:
            messagebox.showwarning("File not found",
                                   f"No file named {self.today_str}.docx or .doc\nin\n{month_dir}")
            return

        try:
            open_with_default_app(doc)
        except Exception as e:
            messagebox.showerror("Open failed", f"Could not open:\n{doc}\n\n{e}")

def main():
    try:
        from ctypes import windll  # type: ignore
        windll.shcore.SetProcessDpiAwareness(1)
    except Exception:
        pass

    app = App()
    try:
        style = ttk.Style()
        if platform.system() == "Darwin":
            style.theme_use("aqua")
        elif platform.system() == "Windows":
            style.theme_use("vista")
    except Exception:
        pass

    app.mainloop()

if __name__ == "__main__":
    main()
